<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闪卡翻转效果</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        /* 添加背景装饰元素 */
        .bg-bubbles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        
        .bg-bubbles li {
            position: absolute;
            list-style: none;
            display: block;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.15);
            bottom: -160px;
            border-radius: 50%;
            animation: square 25s infinite;
            transition-timing-function: linear;
        }
        
        .bg-bubbles li:nth-child(1) {
            left: 10%;
        }
        
        .bg-bubbles li:nth-child(2) {
            left: 20%;
            width: 80px;
            height: 80px;
            animation-delay: 2s;
            animation-duration: 17s;
        }
        
        .bg-bubbles li:nth-child(3) {
            left: 25%;
            animation-delay: 4s;
        }
        
        .bg-bubbles li:nth-child(4) {
            left: 40%;
            width: 60px;
            height: 60px;
            animation-duration: 22s;
            background-color: rgba(255, 255, 255, 0.25);
        }
        
        .bg-bubbles li:nth-child(5) {
            left: 70%;
        }
        
        .bg-bubbles li:nth-child(6) {
            left: 80%;
            width: 120px;
            height: 120px;
            animation-delay: 3s;
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .bg-bubbles li:nth-child(7) {
            left: 32%;
            width: 160px;
            height: 160px;
            animation-delay: 7s;
        }
        
        .bg-bubbles li:nth-child(8) {
            left: 55%;
            width: 20px;
            height: 20px;
            animation-delay: 15s;
            animation-duration: 40s;
        }
        
        @keyframes square {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 1;
                border-radius: 50%;
            }
            100% {
                transform: translateY(-1000px) rotate(720deg);
                opacity: 0;
                border-radius: 50%;
            }
        }

        .app-title {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 2rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .flashcard-container {
            perspective: 1500px; /* 增强3D透视效果 */
            width: 320px;
            height: 220px;
            margin-bottom: 40px;
        }

        .flashcard {
            width: 100%;
            height: 100%;
            position: relative;
            transform-style: preserve-3d;
            transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            box-shadow: 0 15px 35px rgba(0,0,0,0.25);
            border-radius: 15px;
        }

        .flashcard:hover {
            box-shadow: 0 20px 40px rgba(0,0,0,0.4);
        }

        .flashcard.is-flipped {
            transform: rotateY(180deg);
        }

        .flashcard-face {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 26px;
            border-radius: 15px;
            text-align: center;
            padding: 25px;
            box-sizing: border-box;
            transition: all 0.5s ease;
        }

        .flashcard-front {
            background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
            color: #333;
            box-shadow: inset 0 0 50px rgba(255,255,255,0.6);
        }

        .flashcard-back {
            background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%);
            color: #333;
            transform: rotateY(180deg);
            box-shadow: inset 0 0 50px rgba(255,255,255,0.6);
        }

        /* 添加3D边缘效果 */
        .flashcard-face:before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 20px;
            background: linear-gradient(45deg, #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);
            background-size: 400%;
            z-index: -1;
            filter: blur(20px);
            opacity: 0;
            transition: opacity 0.5s;
        }

        .flashcard:hover .flashcard-face:before {
            opacity: 1;
            animation: glowing 20s linear infinite;
        }

        @keyframes glowing {
            0% { background-position: 0 0; }
            50% { background-position: 400% 0; }
            100% { background-position: 0 0; }
        }

        .navigation {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 30px;
        }
        
        /* 滑动动画 */
        .swipe-animation {
            transition: transform 0.3s ease-out;
        }
        
        .swipe-left {
            transform: translateX(-100%) rotateY(-30deg);
            opacity: 0;
        }
        
        .swipe-right {
            transform: translateX(100%) rotateY(30deg);
            opacity: 0;
        }
        
        .flashcard-container {
            perspective: 1500px;
            width: 320px;
            height: 220px;
            margin-bottom: 40px;
            transition: transform 0.3s ease-out;
        }

        button {
            padding: 12px 30px;
            font-size: 18px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.5);
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(0,0,0,0.3);
        }

        #counter {
            font-size: 22px;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 5px rgba(0,0,0,0.3);
            min-width: 80px;
            text-align: center;
        }
    </style>
</head>
<body>

    <!-- 背景装饰元素 -->
    <ul class="bg-bubbles">
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
    </ul>

    <h1 class="app-title">智能抽认卡</h1>

    <div class="flashcard-container">
        <div class="flashcard" id="flashcard">
            <div class="flashcard-face flashcard-front" id="card-front">
                <!-- 问题将由JavaScript填充 -->
            </div>
            <div class="flashcard-face flashcard-back" id="card-back">
                <!-- 答案将由JavaScript填充 -->
            </div>
        </div>
    </div>

    <div class="navigation">
        <span id="counter">1/5</span>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const flashcard = document.getElementById('flashcard');
            const cardFront = document.getElementById('card-front');
            const cardBack = document.getElementById('card-back');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            const counter = document.getElementById('counter');

            // 直接嵌入卡片数据
            const flashcards = [
                { question: "HTML代表什么？", answer: "超文本标记语言" },
                { question: "CSS代表什么？", answer: "层叠样式表" },
                { question: "JavaScript是什么？", answer: "一种编程语言" },
                { question: "DOM是什么？", answer: "文档对象模型" },
                { question: "API是什么？", answer: "应用程序编程接口" }
            ];

            let currentIndex = 0;

            // 更新卡片内容
            function updateCard() {
                const card = flashcards[currentIndex];
                cardFront.textContent = `问题：${card.question}`;
                cardBack.textContent = `答案：${card.answer}`;
                counter.textContent = `${currentIndex + 1}/${flashcards.length}`;
                
                // 重置翻转状态
                flashcard.classList.remove('is-flipped');
            }

            // 初始化卡片
            updateCard();

            // 卡片点击翻转
            flashcard.addEventListener('click', () => {
                flashcard.classList.toggle('is-flipped');
            });

            // 滑动切换卡片功能
            let touchStartX = 0;
            let touchEndX = 0;

            // 触摸事件
            flashcard.addEventListener('touchstart', e => {
                touchStartX = e.changedTouches[0].screenX;
            });

            flashcard.addEventListener('touchend', e => {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });

            // 鼠标事件
            flashcard.addEventListener('mousedown', e => {
                touchStartX = e.screenX;
                document.addEventListener('mouseup', onMouseUp);
            });

            function onMouseUp(e) {
                touchEndX = e.screenX;
                handleSwipe();
                document.removeEventListener('mouseup', onMouseUp);
            }

            function handleSwipe() {
                const minSwipeDistance = 50;
                const distance = touchEndX - touchStartX;

                if (Math.abs(distance) < minSwipeDistance) return;

                if (distance > 0) {
                    // 右滑 - 上一张
                    if (currentIndex > 0) {
                        currentIndex--;
                        animateSwipe('right');
                    }
                } else {
                    // 左滑 - 下一张
                    if (currentIndex < flashcards.length - 1) {
                        currentIndex++;
                        animateSwipe('left');
                    }
                }
            }

            // 滑动动画
            function animateSwipe(direction) {
                const container = document.querySelector('.flashcard-container');
                const card = document.getElementById('flashcard');
                
                // 添加动画类
                container.classList.add('swipe-animation');
                card.classList.add(direction === 'left' ? 'swipe-left' : 'swipe-right');
                
                // 动画结束后更新卡片
                setTimeout(() => {
                    container.classList.remove('swipe-animation');
                    card.classList.remove('swipe-left', 'swipe-right');
                    updateCard();
                }, 300);
            }
        });
    </script>

</body>
</html>